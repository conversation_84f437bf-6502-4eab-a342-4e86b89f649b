package com.k2fsa.sherpa.onnx

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

/**
 * 会议记录列表页面
 * 采用苹果设计风格的简洁界面
 */
class MeetingRecordsActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "MeetingRecordsActivity"
    }

    private lateinit var recyclerView: RecyclerView
    private lateinit var emptyView: LinearLayout
    private lateinit var tvEmptyMessage: TextView
    private lateinit var btnBack: ImageButton
    private lateinit var btnClearAll: ImageButton
    private lateinit var btnImportAudioRecords: ImageButton
    private lateinit var tvTitle: TextView
    private lateinit var tvRecordCount: TextView

    private lateinit var meetingRecordManager: MeetingRecordManager
    private lateinit var adapter: MeetingRecordsAdapter
    private var meetingRecords = mutableListOf<MeetingRecord>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_meeting_records)

        initViews()
        initMeetingRecordManager()
        setupRecyclerView()
        loadMeetingRecords()
    }

    private fun initViews() {
        recyclerView = findViewById(R.id.recycler_meeting_records)
        emptyView = findViewById(R.id.ll_empty_view)
        tvEmptyMessage = findViewById(R.id.tv_empty_message)
        btnBack = findViewById(R.id.btn_back)
        btnClearAll = findViewById(R.id.btn_clear_all)
        btnImportAudioRecords = findViewById(R.id.btn_import_audio_records)
        tvTitle = findViewById(R.id.tv_title)
        tvRecordCount = findViewById(R.id.tv_record_count)

        // 设置点击事件
        btnBack.setOnClickListener { finish() }
        btnClearAll.setOnClickListener { showClearAllDialog() }
        btnImportAudioRecords.setOnClickListener { openMainActivityForImport() }

        // 设置标题
        tvTitle.text = "📝 会议记录"
    }

    private fun initMeetingRecordManager() {
        meetingRecordManager = MeetingRecordManager.getInstance(this)
    }

    private fun setupRecyclerView() {
        adapter = MeetingRecordsAdapter(meetingRecords) { meetingRecord ->
            openMeetingDetail(meetingRecord)
        }
        
        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = adapter
    }

    private fun loadMeetingRecords() {
        try {
            meetingRecords.clear()
            meetingRecords.addAll(meetingRecordManager.getAllRecords())
            
            adapter.notifyDataSetChanged()
            updateUI()
            
            Log.d(TAG, "加载了 ${meetingRecords.size} 条会议记录")
        } catch (e: Exception) {
            Log.e(TAG, "加载会议记录失败", e)
            showToast("加载会议记录失败: ${e.message}")
        }
    }

    private fun updateUI() {
        val recordCount = meetingRecords.size
        
        if (recordCount == 0) {
            recyclerView.visibility = View.GONE
            emptyView.visibility = View.VISIBLE
            tvEmptyMessage.text = "暂无会议记录\n\n开始录音后会自动保存会议记录"
            btnClearAll.visibility = View.GONE
        } else {
            recyclerView.visibility = View.VISIBLE
            emptyView.visibility = View.GONE
            btnClearAll.visibility = View.VISIBLE
        }
        
        tvRecordCount.text = "$recordCount 条记录"
    }

    private fun openMeetingDetail(meetingRecord: MeetingRecord) {
        try {
            val intent = Intent(this, MeetingDetailActivity::class.java)
            intent.putExtra("meeting_record_id", meetingRecord.id)
            startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "打开会议详情失败", e)
            showToast("打开会议详情失败: ${e.message}")
        }
    }

    private fun showClearAllDialog() {
        AlertDialog.Builder(this)
            .setTitle("清空所有记录")
            .setMessage("确定要删除所有会议记录吗？\n\n此操作不可撤销。")
            .setPositiveButton("删除") { _, _ ->
                clearAllRecords()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun clearAllRecords() {
        try {
            val success = meetingRecordManager.clearAllRecords()
            if (success) {
                meetingRecords.clear()
                adapter.notifyDataSetChanged()
                updateUI()
                showToast("所有记录已清空")
                Log.i(TAG, "所有会议记录已清空")
            } else {
                showToast("清空记录失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "清空记录失败", e)
            showToast("清空记录失败: ${e.message}")
        }
    }

    /**
     * 打开主界面进行音频导入
     */
    private fun openMainActivityForImport() {
        try {
            val intent = Intent(this, SingleModelActivity::class.java)
            intent.putExtra("action", "import_audio")
            startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "打开主界面失败", e)
            showToast("打开主界面失败: ${e.message}")
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    override fun onResume() {
        super.onResume()
        // 从详情页返回时重新加载数据
        loadMeetingRecords()
    }
}
