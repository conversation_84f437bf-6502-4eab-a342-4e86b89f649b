<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/apple_system_grouped_background"
    android:fitsSystemWindows="true">

    <!-- Main Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- Header Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="24dp"
            android:paddingBottom="16dp"
            android:background="@color/apple_system_background"
            android:elevation="2dp">

            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_arrow_back_apple"
                android:contentDescription="返回"
                android:layout_marginEnd="16dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📝 会议记录"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Headline"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_record_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0 条记录"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                    android:textColor="@color/apple_secondary_label" />

            </LinearLayout>

            <ImageButton
                android:id="@+id/btn_import_audio_records"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_file_import"
                android:contentDescription="导入音频"
                android:layout_marginStart="8dp" />

            <ImageButton
                android:id="@+id/btn_clear_all"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_delete_apple"
                android:contentDescription="清空所有"
                android:layout_marginStart="8dp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Content Area -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <!-- Meeting Records List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_meeting_records"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="16dp"
                android:clipToPadding="false"
                android:scrollbars="vertical" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/ll_empty_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="48dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📝"
                    android:textSize="64sp"
                    android:layout_marginBottom="24dp"
                    android:alpha="0.3" />

                <TextView
                    android:id="@+id/tv_empty_message"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="暂无会议记录"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                    android:textColor="@color/apple_secondary_label"
                    android:gravity="center"
                    android:lineSpacingExtra="4dp" />

            </LinearLayout>

        </FrameLayout>

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
